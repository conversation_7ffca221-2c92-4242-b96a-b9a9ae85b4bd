# Dynamic Color Theming Implementation - InkSight

## Overview
InkSight's dynamic color theming system implements Material Design 3's Material You principles, providing adaptive color schemes optimized for reading and note-taking while maintaining accessibility and user preference support.

## Color System Architecture

### Material You Integration
- **Source-Based Colors**: Extract colors from user's wallpaper (Android 12+)
- **Fallback Palettes**: Predefined color schemes for unsupported devices
- **Reading Optimization**: Specialized color schemes for extended reading sessions
- **Accessibility Compliance**: WCAG 2.1 AA contrast ratios maintained across all themes

### Color Roles and Mapping

#### Primary Color Roles
```typescript
interface PrimaryColorRoles {
  primary: string;           // Main brand color, key actions
  onPrimary: string;         // Text/icons on primary
  primaryContainer: string;  // Prominent containers
  onPrimaryContainer: string; // Text/icons on primary container
}
```

#### Secondary Color Roles
```typescript
interface SecondaryColorRoles {
  secondary: string;         // Less prominent actions
  onSecondary: string;       // Text/icons on secondary
  secondaryContainer: string; // Secondary containers
  onSecondaryContainer: string; // Text/icons on secondary container
}
```

#### Surface Color Roles
```typescript
interface SurfaceColorRoles {
  surface: string;           // Default surface color
  onSurface: string;         // Text/icons on surface
  surfaceVariant: string;    // Alternative surface color
  onSurfaceVariant: string;  // Text/icons on surface variant
  surfaceContainer: string;  // Container surfaces
  surfaceContainerHigh: string; // Elevated containers
  surfaceContainerHighest: string; // Highest elevation
}
```

## Reading-Optimized Color Schemes

### Light Reading Theme
```typescript
interface LightReadingTheme {
  // Primary colors optimized for reading UI
  primary: '#1976D2';        // Blue for actions and links
  onPrimary: '#FFFFFF';
  primaryContainer: '#E3F2FD';
  onPrimaryContainer: '#0D47A1';
  
  // Reading surface colors
  surface: '#FEFEFE';        // Pure white for maximum contrast
  onSurface: '#1A1A1A';      // Near-black for optimal readability
  surfaceVariant: '#F5F5F5'; // Subtle background variation
  onSurfaceVariant: '#424242';
  
  // Reading-specific colors
  readingSurface: '#FFFFFF';  // Document background
  readingText: '#212121';     // Primary reading text
  readingSecondary: '#757575'; // Secondary text (metadata, etc.)
  
  // Annotation colors
  highlight: {
    yellow: '#FFF59D',
    blue: '#BBDEFB',
    green: '#C8E6C9',
    pink: '#F8BBD9',
    orange: '#FFCC80'
  };
}
```

### Dark Reading Theme
```typescript
interface DarkReadingTheme {
  // Primary colors for dark mode
  primary: '#90CAF9';        // Lighter blue for dark backgrounds
  onPrimary: '#0D47A1';
  primaryContainer: '#1565C0';
  onPrimaryContainer: '#E3F2FD';
  
  // Dark reading surfaces
  surface: '#121212';        // Material Design dark surface
  onSurface: '#E0E0E0';      // High contrast text
  surfaceVariant: '#1E1E1E'; // Elevated surface
  onSurfaceVariant: '#BDBDBD';
  
  // Reading-specific dark colors
  readingSurface: '#1A1A1A';  // Slightly lighter than surface
  readingText: '#E8E8E8';     // Comfortable reading text
  readingSecondary: '#B0B0B0'; // Secondary text
  
  // Dark mode annotation colors
  highlight: {
    yellow: '#F57F17',
    blue: '#1976D2',
    green: '#388E3C',
    pink: '#C2185B',
    orange: '#F57C00'
  };
}
```

### Sepia Reading Theme
```typescript
interface SepiaReadingTheme {
  // Warm, paper-like colors for comfortable reading
  primary: '#8B4513';        // Brown primary for sepia theme
  onPrimary: '#FFF8DC';
  primaryContainer: '#DEB887';
  onPrimaryContainer: '#5D2F02';
  
  // Sepia surfaces mimicking aged paper
  surface: '#F4F1E8';        // Warm paper background
  onSurface: '#5C4B37';      // Dark brown text
  surfaceVariant: '#F0EDE4'; // Slightly darker paper
  onSurfaceVariant: '#6D5A47';
  
  // Reading-specific sepia colors
  readingSurface: '#F7F4EB';  // Main reading background
  readingText: '#4A3728';     // Warm dark brown text
  readingSecondary: '#8B7355'; // Muted secondary text
  
  // Sepia annotation colors
  highlight: {
    yellow: '#F4E04D',
    blue: '#87CEEB',
    green: '#90EE90',
    pink: '#FFB6C1',
    orange: '#FFA500'
  };
}
```

## Dynamic Color Generation

### Material You Color Extraction
```typescript
interface ColorExtractionConfig {
  source: 'wallpaper' | 'image' | 'manual';
  algorithm: 'quantize' | 'dominant' | 'vibrant';
  fallbackPalette: 'blue' | 'green' | 'purple' | 'orange';
  
  // Color harmony rules
  harmony: {
    type: 'analogous' | 'complementary' | 'triadic';
    saturation: 'high' | 'medium' | 'low';
    brightness: 'auto' | 'light' | 'dark';
  };
}
```

### Color Palette Generation
```typescript
interface PaletteGenerator {
  generateFromSource(source: ColorSource): Promise<ColorPalette>;
  validateAccessibility(palette: ColorPalette): AccessibilityReport;
  optimizeForReading(palette: ColorPalette): ReadingOptimizedPalette;
  createVariants(baseColor: string): ColorVariants;
}

interface ColorVariants {
  50: string;   // Lightest
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;  // Base color
  600: string;
  700: string;
  800: string;
  900: string;  // Darkest
}
```

## Adaptive Theming System

### Theme Selection Logic
```typescript
interface ThemeSelector {
  // Automatic theme selection based on context
  selectTheme(context: ThemeContext): ThemeConfiguration;
  
  // User preference integration
  applyUserPreferences(theme: ThemeConfiguration, prefs: UserPreferences): ThemeConfiguration;
  
  // Reading optimization
  optimizeForReading(theme: ThemeConfiguration, document: DocumentType): ThemeConfiguration;
}

interface ThemeContext {
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  ambientLight: 'bright' | 'normal' | 'dim' | 'dark';
  readingDuration: 'short' | 'medium' | 'long';
  documentType: 'text' | 'pdf' | 'image' | 'mixed';
}
```

### Contextual Color Adaptation
```typescript
interface ContextualAdaptation {
  // Time-based adaptation
  timeBasedColors: {
    morning: { warmth: 0.1, brightness: 1.0 };
    afternoon: { warmth: 0.0, brightness: 0.9 };
    evening: { warmth: 0.2, brightness: 0.7 };
    night: { warmth: 0.3, brightness: 0.5 };
  };
  
  // Ambient light adaptation
  lightAdaptation: {
    bright: { contrast: 1.2, saturation: 0.9 };
    normal: { contrast: 1.0, saturation: 1.0 };
    dim: { contrast: 1.1, saturation: 0.8 };
    dark: { contrast: 1.3, saturation: 0.7 };
  };
}
```

## Accessibility and Contrast

### Contrast Ratio Management
```typescript
interface ContrastManager {
  // WCAG compliance checking
  checkContrast(foreground: string, background: string): ContrastResult;
  
  // Automatic contrast adjustment
  adjustForAccessibility(color: string, background: string, target: number): string;
  
  // High contrast mode
  generateHighContrastVariant(theme: ThemeConfiguration): ThemeConfiguration;
}

interface ContrastResult {
  ratio: number;
  wcagAA: boolean;    // 4.5:1 for normal text
  wcagAAA: boolean;   // 7:1 for normal text
  wcagAALarge: boolean; // 3:1 for large text
}
```

### Accessibility Features
```typescript
interface AccessibilityThemeFeatures {
  // High contrast mode
  highContrast: {
    enabled: boolean;
    contrastRatio: 7.0; // WCAG AAA compliance
    reducedColors: boolean; // Simplified color palette
  };
  
  // Color blindness support
  colorBlindSupport: {
    type: 'protanopia' | 'deuteranopia' | 'tritanopia' | 'none';
    simulation: boolean;
    alternativeIndicators: boolean; // Use patterns/shapes instead of color
  };
  
  // Reduced motion
  reducedMotion: {
    enabled: boolean;
    disableTransitions: boolean;
    staticIndicators: boolean;
  };
}
```

## Theme Customization

### User Preference System
```typescript
interface UserThemePreferences {
  // Color preferences
  colorScheme: 'auto' | 'light' | 'dark' | 'sepia';
  accentColor: string;
  dynamicColors: boolean;
  
  // Reading preferences
  readingBackground: 'white' | 'cream' | 'gray' | 'black';
  textColor: 'auto' | 'black' | 'gray' | 'custom';
  highlightColors: string[];
  
  // Accessibility preferences
  highContrast: boolean;
  largeText: boolean;
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
}
```

### Custom Theme Creation
```typescript
interface CustomThemeBuilder {
  // Base theme selection
  selectBaseTheme(type: 'light' | 'dark' | 'sepia'): ThemeConfiguration;
  
  // Color customization
  setAccentColor(color: string): void;
  setSurfaceColors(colors: SurfaceColorSet): void;
  setReadingColors(colors: ReadingColorSet): void;
  
  // Validation and optimization
  validateTheme(): ValidationResult;
  optimizeForReadability(): void;
  exportTheme(): ThemeConfiguration;
}
```

## Implementation Architecture

### Theme Provider Structure
```typescript
interface ThemeProvider {
  // Current theme state
  currentTheme: ThemeConfiguration;
  
  // Theme management
  setTheme(theme: ThemeConfiguration): void;
  updateTheme(updates: Partial<ThemeConfiguration>): void;
  resetToDefault(): void;
  
  // Dynamic updates
  enableDynamicColors(): void;
  updateFromWallpaper(): Promise<void>;
  adaptToContext(context: ThemeContext): void;
  
  // Persistence
  saveTheme(theme: ThemeConfiguration): Promise<void>;
  loadSavedTheme(): Promise<ThemeConfiguration>;
}
```

### Color Token System
```typescript
interface ColorTokens {
  // Semantic color tokens
  semantic: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  
  // Reading-specific tokens
  reading: {
    background: string;
    text: string;
    selection: string;
    cursor: string;
    lineNumbers: string;
  };
  
  // UI component tokens
  components: {
    button: ComponentColorTokens;
    card: ComponentColorTokens;
    navigation: ComponentColorTokens;
    input: ComponentColorTokens;
  };
}
```

## Performance Optimization

### Color Caching Strategy
```typescript
interface ColorCache {
  // Generated color palettes
  palettes: Map<string, ColorPalette>;
  
  // Computed contrast ratios
  contrastCache: Map<string, number>;
  
  // Theme variations
  themeVariants: Map<string, ThemeConfiguration>;
  
  // Cache management
  clearCache(): void;
  preloadCommonThemes(): Promise<void>;
  optimizeCache(): void;
}
```

### Efficient Color Updates
```typescript
interface ColorUpdateStrategy {
  // Batch color updates
  batchUpdate(updates: ColorUpdate[]): void;
  
  // Selective component updates
  updateComponent(component: string, colors: ComponentColors): void;
  
  // Transition animations
  animateColorChange(from: ThemeConfiguration, to: ThemeConfiguration): void;
  
  // Performance monitoring
  measureUpdatePerformance(): PerformanceMetrics;
}
```

## Testing and Validation

### Color Testing Framework
```typescript
interface ColorTestSuite {
  // Accessibility testing
  testContrastRatios(theme: ThemeConfiguration): ContrastTestResult[];
  testColorBlindness(theme: ThemeConfiguration): ColorBlindTestResult[];
  
  // Visual testing
  generateColorSwatches(theme: ThemeConfiguration): ColorSwatch[];
  testReadability(theme: ThemeConfiguration): ReadabilityScore;
  
  // Performance testing
  measureColorGenerationTime(): number;
  testThemeTransitionPerformance(): PerformanceMetrics;
}
```

### Quality Assurance
- **Automated Testing**: Contrast ratio validation for all color combinations
- **Visual Regression**: Screenshot comparison for theme changes
- **Accessibility Audit**: Regular accessibility compliance checking
- **User Testing**: Readability and preference validation with real users

---

**Document Version**: 1.0  
**Last Updated**: June 21, 2025  
**Author**: InkSight Design System Team  
**Reviewers**: UX Designer, Accessibility Specialist, Frontend Developer
