# Key Screen Mockups and Wireframes - InkSight

## Overview
This document provides detailed wireframes and design specifications for InkSight's core user interfaces, focusing on the reading interface, note-taking flow, and handwriting digitization screens using Material Design 3 principles.

## Screen Architecture Overview

### Information Architecture
```
InkSight App Structure
├── Onboarding Flow
│   ├── Welcome Screen
│   ├── Privacy Explanation
│   ├── Feature Overview
│   └── Permission Setup
├── Main Application
│   ├── Document Library
│   ├── Reading Interface
│   ├── Note-Taking Flow
│   ├── Handwriting Capture
│   ├── Search & Discovery
│   └── Settings & Preferences
└── Contextual Flows
    ├── Document Import
    ├── Annotation Management
    ├── Focus Mode
    └── Export & Sharing
```

## Document Library Screen

### Layout Structure
```
┌─────────────────────────────────────┐
│ [☰] InkSight Library        [⚙️][🔍] │ ← Top App Bar
├─────────────────────────────────────┤
│ Recent Documents                    │
│ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐    │ ← Horizontal Scroll
│ │ Doc │ │ Doc │ │ Doc │ │ Doc │    │
│ │  1  │ │  2  │ │  3  │ │  4  │    │
│ └─────┘ └─────┘ └─────┘ └─────┘    │
│                                     │
│ All Documents                       │
│ ┌─────────────────────────────────┐ │
│ │ 📄 Document Title               │ │ ← List Items
│ │    Author • Date • 2.3 MB      │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 📄 Another Document             │ │
│ │    Author • Date • 1.8 MB      │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [📚] [📖] [📝] [🔍] [⚙️]           │ ← Bottom Navigation
└─────────────────────────────────────┘
```

### Component Specifications
```typescript
interface LibraryScreenComponents {
  topAppBar: {
    type: 'center_aligned';
    title: 'InkSight Library';
    navigationIcon: 'menu';
    actions: ['settings', 'search'];
  };
  
  recentDocuments: {
    component: 'horizontal_scroll_view';
    itemType: 'card';
    itemSize: { width: 120, height: 160 };
    spacing: 12;
  };
  
  documentList: {
    component: 'list_view';
    itemType: 'three_line_list_item';
    leadingIcon: 'document_type_icon';
    trailingIcon: 'more_vert';
  };
  
  bottomNavigation: {
    destinations: ['library', 'reading', 'notes', 'search', 'settings'];
    activeIndicator: 'pill';
  };
}
```

## Reading Interface Screen

### Portrait Layout
```
┌─────────────────────────────────────┐
│ [←] Document Title           [⋮]    │ ← Top App Bar (Auto-hide)
├─────────────────────────────────────┤
│                                     │
│  Chapter 1: Introduction            │
│                                     │
│  Lorem ipsum dolor sit amet,        │
│  consectetur adipiscing elit.       │
│  Sed do eiusmod tempor incididunt   │ ← Document Content
│  ut labore et dolore magna aliqua.  │
│  Ut enim ad minim veniam, quis      │
│  nostrud exercitation ullamco       │
│  laboris nisi ut aliquip ex ea      │
│  commodo consequat.                 │
│                                     │
│  [Selected text highlighted]        │ ← Text Selection
│                                     │
├─────────────────────────────────────┤
│ ████████████████░░░░░░░░░░░░░░░░░░░ │ ← Reading Progress
├─────────────────────────────────────┤
│                               [📝]  │ ← Floating Action Button
└─────────────────────────────────────┘
```

### Landscape Layout (Split-Screen)
```
┌─────────────────────────────────────────────────────────────┐
│ [←] Document Title                                    [⋮]   │
├─────────────────────────────┬───────────────────────────────┤
│                             │ 📑 Annotations                │
│  Chapter 1: Introduction    │ ┌─────────────────────────────┐│
│                             │ │ "Important concept"         ││
│  Lorem ipsum dolor sit      │ │ Page 15 • 2 hours ago      ││
│  amet, consectetur          │ └─────────────────────────────┘│
│  adipiscing elit. Sed do    │ ┌─────────────────────────────┐│
│  eiusmod tempor incididunt  │ │ "Key takeaway"              ││
│  ut labore et dolore magna  │ │ Page 23 • 1 hour ago       ││
│  aliqua. Ut enim ad minim   │ └─────────────────────────────┘│
│  veniam, quis nostrud       │                               │
│  exercitation ullamco       │ 🔖 Bookmarks                  │
│  laboris nisi ut aliquip    │ • Chapter 1: Introduction     │
│  ex ea commodo consequat.   │ • Section 2.3: Analysis      │
│                             │ • Conclusion                  │
├─────────────────────────────┴───────────────────────────────┤
│ ████████████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ │
└─────────────────────────────────────────────────────────────┘
```

### Component Specifications
```typescript
interface ReadingInterfaceComponents {
  topAppBar: {
    type: 'center_aligned';
    scrollBehavior: 'hide_on_scroll';
    title: 'document.title';
    navigationIcon: 'arrow_back';
    actions: ['bookmark_add', 'text_format', 'more_vert'];
  };
  
  documentViewer: {
    component: 'scrollable_content';
    textSelectionEnabled: true;
    zoomEnabled: true;
    pageTransition: 'smooth_scroll';
  };
  
  progressIndicator: {
    type: 'linear';
    position: 'bottom';
    color: 'primary';
    variant: 'determinate';
  };
  
  floatingActionButton: {
    icon: 'edit_note';
    position: 'bottom_right';
    action: 'create_annotation';
  };
}
```

## Note-Taking Flow

### Annotation Creation
```
┌─────────────────────────────────────┐
│ [×] Add Note                  [✓]   │ ← Modal Header
├─────────────────────────────────────┤
│ Selected Text:                      │
│ ┌─────────────────────────────────┐ │
│ │ "Lorem ipsum dolor sit amet,    │ │ ← Selected Text
│ │ consectetur adipiscing elit"    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Your Note:                          │
│ ┌─────────────────────────────────┐ │
│ │ This is an important concept    │ │ ← Text Input
│ │ that relates to...              │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Highlight Color:                    │
│ ● ○ ○ ○ ○                          │ ← Color Selection
│                                     │
│ Tags: #important #chapter1          │ ← Tag Input
├─────────────────────────────────────┤
│           [Cancel] [Save Note]      │ ← Action Buttons
└─────────────────────────────────────┘
```

### Notes Management
```
┌─────────────────────────────────────┐
│ [←] My Notes              [🔍] [⋮]  │ ← Top App Bar
├─────────────────────────────────────┤
│ Filter: [All] [Highlights] [Notes]  │ ← Filter Chips
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ ● "Important concept"           │ │ ← Note Item
│ │   This is an important concept  │ │
│ │   Document Title • Page 15      │ │
│ │   2 hours ago                   │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ ● "Key takeaway"                │ │
│ │   Remember this for the exam    │ │
│ │   Another Document • Page 23    │ │
│ │   1 hour ago                    │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [📚] [📖] [📝] [🔍] [⚙️]           │ ← Bottom Navigation
└─────────────────────────────────────┘
```

## Handwriting Digitization Flow

### Camera Capture Screen
```
┌─────────────────────────────────────┐
│ [×] Capture Handwriting       [💡]  │ ← Top App Bar
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │        Camera Viewfinder        │ │ ← Camera Preview
│ │                                 │ │
│ │     [Handwritten text here]     │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Tips: • Ensure good lighting        │ ← Guidance Text
│       • Keep text horizontal       │
│       • Avoid shadows              │
│                                     │
├─────────────────────────────────────┤
│ [📁] [📷] [🔄]                     │ ← Action Buttons
│ Gallery Capture Retake             │
└─────────────────────────────────────┘
```

### Processing and Results
```
┌─────────────────────────────────────┐
│ [←] Recognition Results       [⋮]   │ ← Top App Bar
├─────────────────────────────────────┤
│ Original Image:                     │
│ ┌─────────────────────────────────┐ │
│ │ [Captured handwriting image]    │ │ ← Original Image
│ └─────────────────────────────────┘ │
│                                     │
│ Recognized Text:                    │
│ ┌─────────────────────────────────┐ │
│ │ This is the recognized text     │ │ ← Editable Text
│ │ from the handwriting sample.    │ │
│ │ Confidence: 89%                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Language: English ▼                 │ ← Language Selector
│                                     │
├─────────────────────────────────────┤
│ [Retake] [Edit] [Save] [Share]      │ ← Action Buttons
└─────────────────────────────────────┘
```

## Search Interface

### Search Screen
```
┌─────────────────────────────────────┐
│ [←] [Search documents and notes...] │ ← Search Bar
├─────────────────────────────────────┤
│ Recent Searches:                    │
│ • "machine learning"                │ ← Recent Searches
│ • "chapter 3"                       │
│ • "important concepts"              │
│                                     │
│ Suggestions:                        │
│ • Search in annotations             │ ← Search Suggestions
│ • Search by document type           │
│ • Search by date range              │
│                                     │
│ Quick Filters:                      │
│ [PDFs] [Notes] [Recent] [Bookmarks] │ ← Filter Chips
├─────────────────────────────────────┤
│ [📚] [📖] [📝] [🔍] [⚙️]           │ ← Bottom Navigation
└─────────────────────────────────────┘
```

### Search Results
```
┌─────────────────────────────────────┐
│ [←] [machine learning          ] [×]│ ← Search Bar with Query
├─────────────────────────────────────┤
│ 23 results • Sort: Relevance ▼     │ ← Results Header
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ 📄 Introduction to ML           │ │ ← Search Result
│ │ ...concepts of machine learning │ │
│ │ are fundamental to...           │ │
│ │ Document • Page 15 • 89% match  │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 📝 My Notes on ML               │ │
│ │ Machine learning algorithms...  │ │
│ │ Note • Created 2 days ago       │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [📚] [📖] [📝] [🔍] [⚙️]           │ ← Bottom Navigation
└─────────────────────────────────────┘
```

## Focus Mode Interface

### Focus Mode Setup
```
┌─────────────────────────────────────┐
│ [×] Focus Mode Setup          [?]   │ ← Modal Header
├─────────────────────────────────────┤
│ Reading Goal:                       │
│ ○ Time-based (25 minutes)           │ ← Goal Selection
│ ● Page-based (10 pages)             │
│ ○ Chapter-based (1 chapter)         │
│                                     │
│ Focus Level:                        │
│ ○ Light (hide notifications)        │ ← Focus Level
│ ● Medium (minimal UI)               │
│ ○ Deep (full immersion)             │
│                                     │
│ Break Reminders:                    │
│ ☑ Every 25 minutes                  │ ← Options
│ ☑ Eye strain reminders              │
│ ☐ Posture reminders                 │
│                                     │
├─────────────────────────────────────┤
│           [Cancel] [Start Focus]    │ ← Action Buttons
└─────────────────────────────────────┘
```

### Active Focus Mode
```
┌─────────────────────────────────────┐
│                                     │
│                                     │
│  Chapter 1: Introduction            │
│                                     │
│  Lorem ipsum dolor sit amet,        │ ← Minimal UI
│  consectetur adipiscing elit.       │
│  Sed do eiusmod tempor incididunt   │
│  ut labore et dolore magna aliqua.  │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
├─────────────────────────────────────┤
│ 🎯 7/10 pages • 18:32 remaining     │ ← Progress Indicator
└─────────────────────────────────────┘
```

## Design System Integration

### Material Design 3 Components Used
```typescript
interface ScreenComponents {
  // Navigation
  topAppBar: 'CenterAlignedTopAppBar';
  bottomNavigation: 'NavigationBar';
  navigationDrawer: 'ModalNavigationDrawer';
  
  // Content
  card: 'Card';
  listItem: 'ListItem';
  chip: 'FilterChip';
  
  // Input
  textField: 'OutlinedTextField';
  searchBar: 'DockedSearchBar';
  slider: 'Slider';
  
  // Actions
  button: 'FilledButton';
  iconButton: 'IconButton';
  fab: 'FloatingActionButton';
  
  // Feedback
  progressIndicator: 'LinearProgressIndicator';
  snackbar: 'Snackbar';
  dialog: 'AlertDialog';
}
```

### Accessibility Features
- **Screen Reader Support**: Semantic labels and content descriptions
- **Keyboard Navigation**: Full keyboard accessibility
- **High Contrast**: Support for high contrast themes
- **Large Text**: Scalable text with user preferences
- **Touch Targets**: Minimum 48dp touch targets
- **Focus Indicators**: Clear focus indication for all interactive elements

---

**Document Version**: 1.0  
**Last Updated**: June 21, 2025  
**Author**: InkSight UX Design Team  
**Reviewers**: Product Designer, Accessibility Specialist, Frontend Developer
