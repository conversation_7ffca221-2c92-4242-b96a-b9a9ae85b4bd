# File Handling and Storage Architecture - InkSight

## Overview
InkSight's file handling and storage architecture provides efficient, secure, and scalable document management with support for 9 file formats, encrypted local storage, and optimized performance for mobile devices.

## Storage Architecture Overview

### Storage Hierarchy
```
InkSight Storage Architecture
├── Application Storage
│   ├── Documents Directory
│   │   ├── Imported Documents
│   │   ├── Processed Documents
│   │   └── Document Metadata
│   ├── Database Storage
│   │   ├── SQLite Database (Encrypted)
│   │   ├── Search Indexes (FTS5)
│   │   └── User Preferences
│   ├── Cache Storage
│   │   ├── Rendered Pages
│   │   ├── Thumbnails
│   │   └── AI Model Cache
│   └── Secure Storage
│       ├── Encryption Keys
│       ├── User Credentials
│       └── Biometric Data
├── System Integration
│   ├── Document Provider
│   ├── File System Access
│   └── External Storage
└── Backup and Recovery
    ├── Local Backups
    ├── Export Functionality
    └── Data Recovery
```

## Document Management System

### File Format Support Architecture
```typescript
interface DocumentFormatSupport {
  supportedFormats: {
    epub: {
      parser: 'epub.js';
      features: ['chapters', 'toc', 'metadata', 'images'];
      encryption: 'supported';
    };
    pdf: {
      parser: 'react-native-pdf';
      features: ['pages', 'annotations', 'forms', 'images'];
      encryption: 'supported';
    };
    docx: {
      parser: 'mammoth.js';
      features: ['styles', 'images', 'tables', 'headers'];
      encryption: 'supported';
    };
    doc: {
      parser: 'antiword + custom';
      features: ['basic_text', 'formatting'];
      encryption: 'limited';
    };
    rtf: {
      parser: 'rtf.js';
      features: ['rich_text', 'formatting', 'images'];
      encryption: 'supported';
    };
    txt: {
      parser: 'native';
      features: ['plain_text', 'encoding_detection'];
      encryption: 'supported';
    };
    djvu: {
      parser: 'djvu.js';
      features: ['pages', 'text_layer', 'images'];
      encryption: 'limited';
    };
    fb2: {
      parser: 'custom_xml';
      features: ['chapters', 'metadata', 'images'];
      encryption: 'supported';
    };
    mobi: {
      parser: 'mobi.js';
      features: ['chapters', 'toc', 'images'];
      encryption: 'limited';
    };
    chm: {
      parser: 'chm.js';
      features: ['help_topics', 'navigation', 'search'];
      encryption: 'limited';
    };
  };
}
```

### Document Processing Pipeline
```typescript
interface DocumentProcessingPipeline {
  stages: {
    import: {
      fileValidation: 'format and integrity checking';
      virusScanning: 'basic malware detection';
      sizeValidation: 'file size limits';
      formatDetection: 'automatic format identification';
    };
    
    parsing: {
      formatSpecificParsing: 'specialized parser for each format';
      metadataExtraction: 'title, author, creation date, etc.';
      contentExtraction: 'text, images, structure';
      errorHandling: 'graceful failure and recovery';
    };
    
    processing: {
      textNormalization: 'encoding and formatting normalization';
      imageOptimization: 'compression and format conversion';
      structureAnalysis: 'chapter and section detection';
      indexGeneration: 'search index creation';
    };
    
    storage: {
      encryption: 'AES-256 encryption for sensitive content';
      compression: 'optional compression for large files';
      deduplication: 'duplicate content detection';
      versioning: 'document version management';
    };
  };
}
```

## Database Architecture

### SQLite Schema Design
```sql
-- Documents table
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    title TEXT,
    author TEXT,
    format TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    page_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_opened TIMESTAMP,
    reading_position TEXT, -- JSON: {page, offset, percentage}
    metadata TEXT, -- JSON: format-specific metadata
    checksum TEXT, -- File integrity verification
    encryption_key_id TEXT, -- Reference to encryption key
    INDEX idx_documents_format (format),
    INDEX idx_documents_modified (modified_at),
    INDEX idx_documents_opened (last_opened)
);

-- Annotations table
CREATE TABLE annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    type TEXT NOT NULL, -- 'highlight', 'note', 'bookmark'
    content TEXT,
    position TEXT NOT NULL, -- JSON: position information
    color TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tags TEXT, -- JSON: array of tags
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_annotations_document (document_id),
    INDEX idx_annotations_type (type),
    INDEX idx_annotations_created (created_at)
);

-- Full-text search table
CREATE VIRTUAL TABLE document_search USING fts5(
    document_id,
    title,
    author,
    content,
    content='documents',
    content_rowid='id'
);

-- User preferences table
CREATE TABLE user_preferences (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    type TEXT NOT NULL, -- 'string', 'number', 'boolean', 'json'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reading sessions table
CREATE TABLE reading_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    pages_read INTEGER DEFAULT 0,
    words_read INTEGER DEFAULT 0,
    session_type TEXT, -- 'focus', 'casual', 'research'
    goals TEXT, -- JSON: session goals
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_sessions_document (document_id),
    INDEX idx_sessions_start (start_time)
);
```

### Database Encryption
```typescript
interface DatabaseEncryption {
  encryptionMethod: 'SQLCipher';
  keyDerivation: {
    algorithm: 'PBKDF2';
    iterations: 100000;
    saltLength: 32;
    keyLength: 256;
  };
  
  performance: {
    encryptionOverhead: '<10%';
    queryPerformance: 'minimal impact';
    indexSupport: 'full support';
  };
  
  keyManagement: {
    storage: 'device keystore';
    rotation: 'annual key rotation';
    backup: 'secure key backup';
  };
}
```

## File System Organization

### Directory Structure
```
InkSight/
├── Documents/
│   ├── imported/           # Original imported documents
│   ├── processed/          # Processed and optimized documents
│   ├── thumbnails/         # Document thumbnail images
│   └── temp/              # Temporary processing files
├── Database/
│   ├── inksight.db        # Main SQLite database
│   ├── search.db          # Search index database
│   └── backups/           # Database backups
├── Cache/
│   ├── pages/             # Rendered page cache
│   ├── images/            # Optimized image cache
│   ├── models/            # AI model cache
│   └── thumbnails/        # Thumbnail cache
├── Exports/
│   ├── annotations/       # Exported annotations
│   ├── summaries/         # Exported summaries
│   └── documents/         # Exported documents
└── Logs/
    ├── application.log    # Application logs
    ├── security.log       # Security event logs
    └── performance.log    # Performance metrics
```

### File Naming Convention
```typescript
interface FileNamingConvention {
  documents: {
    pattern: '{uuid}_{timestamp}_{original_name}';
    example: 'a1b2c3d4_20250621_document.pdf';
  };
  
  thumbnails: {
    pattern: '{document_uuid}_thumb_{size}.{format}';
    example: 'a1b2c3d4_thumb_256.webp';
  };
  
  cache: {
    pattern: '{document_uuid}_page_{page_number}_{resolution}.{format}';
    example: 'a1b2c3d4_page_001_1080.webp';
  };
  
  exports: {
    pattern: '{export_type}_{document_title}_{timestamp}.{format}';
    example: 'annotations_MyDocument_20250621.json';
  };
}
```

## Storage Optimization

### Compression Strategy
```typescript
interface CompressionStrategy {
  documents: {
    text: 'gzip compression for text-heavy documents';
    images: 'WebP format with quality optimization';
    mixed: 'selective compression based on content type';
  };
  
  cache: {
    pages: 'WebP with 85% quality for rendered pages';
    thumbnails: 'WebP with 70% quality for thumbnails';
    temporary: 'no compression for temporary files';
  };
  
  database: {
    content: 'SQLite built-in compression';
    indexes: 'compressed indexes where beneficial';
    backups: 'gzip compression for backup files';
  };
}
```

### Caching Architecture
```typescript
interface CachingArchitecture {
  levels: {
    memory: {
      size: '50MB';
      content: 'frequently accessed pages and metadata';
      eviction: 'LRU (Least Recently Used)';
    };
    
    disk: {
      size: '500MB';
      content: 'rendered pages, thumbnails, processed content';
      eviction: 'LFU (Least Frequently Used)';
    };
    
    persistent: {
      size: 'unlimited';
      content: 'original documents, annotations, user data';
      eviction: 'manual cleanup only';
    };
  };
  
  strategies: {
    preloading: 'predictive page preloading';
    backgroundSync: 'background cache warming';
    intelligentEviction: 'usage pattern-based eviction';
  };
}
```

## Data Migration and Backup

### Backup Strategy
```typescript
interface BackupStrategy {
  automatic: {
    frequency: 'daily';
    retention: '30 days';
    content: 'database, user preferences, annotations';
    location: 'local device storage';
  };
  
  manual: {
    userInitiated: 'export functionality';
    content: 'complete data export';
    formats: ['JSON', 'CSV', 'PDF'];
    encryption: 'optional user-controlled encryption';
  };
  
  recovery: {
    automaticRestore: 'on app reinstall';
    manualRestore: 'import from backup file';
    validation: 'integrity checking on restore';
  };
}
```

### Data Migration
```typescript
interface DataMigration {
  versionMigration: {
    strategy: 'incremental schema updates';
    rollback: 'automatic rollback on failure';
    validation: 'data integrity verification';
  };
  
  formatMigration: {
    documentFormats: 'automatic format conversion when needed';
    databaseSchema: 'versioned schema migration';
    userPreferences: 'preference format updates';
  };
  
  platformMigration: {
    crossPlatform: 'iOS to Android and vice versa';
    dataFormat: 'platform-agnostic data formats';
    validation: 'cross-platform compatibility checking';
  };
}
```

## Performance Optimization

### I/O Optimization
```typescript
interface IOOptimization {
  readOptimization: {
    sequentialReads: 'optimized for document reading patterns';
    randomAccess: 'efficient page jumping';
    prefetching: 'intelligent content prefetching';
  };
  
  writeOptimization: {
    batchWrites: 'batched annotation saves';
    asyncWrites: 'non-blocking write operations';
    writeCoalescing: 'combine multiple writes';
  };
  
  indexOptimization: {
    searchIndexes: 'optimized FTS5 indexes';
    databaseIndexes: 'query-optimized database indexes';
    cacheIndexes: 'fast cache lookup indexes';
  };
}
```

### Memory Management
```typescript
interface MemoryManagement {
  documentLoading: {
    lazyLoading: 'load pages on demand';
    memoryMapping: 'memory-mapped file access where possible';
    resourcePooling: 'reuse document parser instances';
  };
  
  cacheManagement: {
    memoryPressure: 'automatic cache cleanup on memory pressure';
    sizeMonitoring: 'continuous memory usage monitoring';
    leakPrevention: 'automatic leak detection and cleanup';
  };
  
  garbageCollection: {
    strategy: 'minimize GC pressure';
    objectPooling: 'reuse expensive objects';
    weakReferences: 'use weak references for caches';
  };
}
```

## Security and Privacy

### File Security
```typescript
interface FileSecurity {
  encryption: {
    algorithm: 'AES-256-GCM';
    keyManagement: 'hardware-backed key storage';
    fileLevel: 'per-file encryption keys';
  };
  
  accessControl: {
    permissions: 'minimal file system permissions';
    sandboxing: 'app sandbox enforcement';
    validation: 'file integrity validation';
  };
  
  privacy: {
    noCloudSync: 'no automatic cloud synchronization';
    localOnly: 'all data remains on device';
    secureDelete: 'secure file deletion';
  };
}
```

### Data Integrity
```typescript
interface DataIntegrity {
  checksums: {
    algorithm: 'SHA-256';
    verification: 'automatic integrity checking';
    corruption: 'corruption detection and recovery';
  };
  
  validation: {
    import: 'file validation on import';
    runtime: 'runtime data validation';
    export: 'export data validation';
  };
  
  recovery: {
    corruption: 'automatic corruption recovery';
    backup: 'restore from backup on corruption';
    partial: 'partial data recovery when possible';
  };
}
```

## Testing and Validation

### Storage Testing
```typescript
interface StorageTesting {
  performance: {
    loadTesting: 'large document handling';
    stressTesting: 'high concurrent access';
    memoryTesting: 'memory usage under load';
  };
  
  reliability: {
    corruptionTesting: 'data corruption scenarios';
    recoveryTesting: 'backup and recovery validation';
    migrationTesting: 'data migration validation';
  };
  
  security: {
    encryptionTesting: 'encryption/decryption validation';
    accessTesting: 'unauthorized access prevention';
    leakTesting: 'data leak prevention';
  };
}
```

---

**Document Version**: 1.0  
**Last Updated**: June 21, 2025  
**Author**: InkSight Storage Architecture Team  
**Reviewers**: Technical Lead, Security Engineer, Performance Engineer
